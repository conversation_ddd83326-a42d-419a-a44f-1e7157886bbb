# Nexus Trader Terminal

A professional personal trading terminal built as a React single-page application. Provides real-time trading simulation, position management, and advanced charting with technical analysis for individual traders.

![Nexus Trader Terminal](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)
![React](https://img.shields.io/badge/React-18.3.1-blue)
![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue)
![Vite](https://img.shields.io/badge/Vite-5.4.1-purple)

## 🚀 Features

### Core Trading Functionality
- **Real-time Trading Simulation**: Live market data simulation with realistic price movements
- **Position Management**: Mock trading positions with live P&L tracking
- **Order Management**: Simulated order placement and management
- **Live Price Feeds**: Real-time currency pair prices with 8 major trading pairs
- **Personal Trading Dashboard**: Single-trader focused interface

### Professional Charting
- **KLineChart Integration**: Professional trading charts with technical indicators
- **Technical Analysis**: Moving Averages, RSI, MACD, Volume indicators  
- **Multiple Timeframes**: 1-minute to daily charts with real-time data
- **Interactive Charts**: Professional trading interface with live price overlay
- **Mini Charts**: Compact chart views with recent price history

### Trading Interface
- **Three-Column Layout**: Optimized desktop trading workstation design
- **Watchlist**: Interactive currency pair selection with price flash animations
- **Order Ticket**: Trading interface for simulated order placement
- **Real-time Updates**: All components update automatically with live price data
- **Professional Styling**: Trading-optimized color schemes and visual feedback

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React 18.3.1 with TypeScript and Vite 5.4.1
- **UI Components**: shadcn/ui component library (streamlined selection)
- **Charts**: KLineChart v10.0.0-alpha5 for professional trading charts
- **State Management**: React Context API with TradingContext
- **Routing**: React Router DOM v6 (single-page application)
- **Styling**: Tailwind CSS v3 with custom trading design system

### Application Architecture
```
┌─────────────────────────────────────────────────────────────┐
│ TradingHeader (Nexus Trader Terminal)                       │
├─────────────────────────────────────────────────────────────┤
│ Left Sidebar    │ Main Chart Area    │ Right Sidebar        │
│ (Mini Chart)    │ (Professional      │ (Messages &          │
│ (Watchlist)     │  KLineChart)       │  Positions)          │
│ (Order Ticket)  │                    │                      │
├─────────────────┴────────────────────┴──────────────────────┤
│ Bottom Panel (Tabbed: Orders, Positions, Messages)          │
└─────────────────────────────────────────────────────────────┘
```

### Current Project Structure
```
src/
├── components/
│   ├── ui/                    # shadcn/ui components (essential set)
│   ├── BottomPanel.tsx        # Tabbed data display
│   ├── ErrorBoundary.tsx      # Error handling
│   ├── MainChart.tsx          # Professional KLineChart component
│   ├── MessagesPanel.tsx      # Trading notifications
│   ├── MiniChart.tsx          # Compact chart view
│   ├── OrderTicket.tsx        # Trading interface
│   ├── PositionsPanel.tsx     # Position management
│   ├── TradingHeader.tsx      # Application header
│   └── Watchlist.tsx          # Currency pair selection
├── contexts/
│   └── TradingContext.tsx     # Central trading state & simulation
├── hooks/
│   ├── use-mobile.tsx         # Mobile device detection
│   ├── use-toast.ts           # Toast notifications
│   └── useKLineData.ts        # Chart data generation
├── pages/
│   └── Index.tsx              # Main application layout
├── types/
│   └── trading.ts             # TypeScript definitions
├── utils/
│   └── priceFormatting.ts     # Price display utilities
└── lib/
    └── utils.ts               # General utilities
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm package manager

### Installation
```bash
# Clone the repository
git clone https://github.com/[username]/nexus-trader-terminal.git
cd nexus-trader-terminal

# Install dependencies
npm install
```

### Development
```bash
# Start development server (auto-detects available port, typically 8080+)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

### Access
- **URL**: http://localhost:[PORT] (Vite auto-detects available port)
- **No Authentication Required**: Direct access to trading terminal
- **Simulation Mode**: All trading is simulated with mock data

## 📊 Trading Features

### Real-time Market Simulation
- **8 Currency Pairs**: EURUSD, GBPUSD, USDJPY, USDCHF, EURJPY, EURGBP, EURCHF, AUDUSD
- **Live Price Updates**: Prices update every 1.5 seconds with realistic volatility
- **Market Trends**: 5-minute trend cycles with different patterns per currency type
- **Dynamic Spreads**: Bid-ask spreads adjust based on volatility
- **Professional Formatting**: JPY pairs (3 decimals), others (5 decimals)

### Professional Charting
- **Real-time Charts**: Live price updates with automatic chart synchronization
- **Technical Indicators**: MA, Bollinger Bands, RSI, MACD, Volume with interactive controls
- **Timeframe Support**: 1m, 5m, 15m, 30m, 1h, 4h, 1d with proper data aggregation
- **Interactive Interface**: Timeframe buttons, indicator toggles, live price overlay
- **Chart Data Generation**: useKLineData hook converts price ticks to OHLC candlesticks

### Trading Interface
- **Interactive Watchlist**: Click currency pairs to update charts and selections
- **Price Flash Animations**: Visual feedback for price movements (green up, red down)
- **Order Ticket**: Simulated trading interface with order types and controls
- **Position Tracking**: Mock position management with live P&L calculation
- **Real-time Updates**: All components automatically sync with price changes

## 🔧 Configuration

### Development Configuration
- **TypeScript**: Multi-project setup with path aliases (`@/*` → `./src/*`)
- **ESLint**: Flat config with React Hooks and TypeScript integration
- **Vite**: React SWC plugin for fast compilation and hot reload
- **Tailwind**: Custom design system with trading-optimized themes

### Environment
- **No environment variables required**
- **No external API dependencies**
- **Self-contained simulation system**

## 🛠️ Development

### Key Components

#### TradingContext
Central state management for:
- Real-time price simulation for 8 currency pairs
- Selected symbol and timeframe management
- Chart data generation and caching
- Price flash animation states

#### MainChart & MiniChart
- **MainChart**: Professional KLineChart with full technical analysis
- **MiniChart**: Compact 20-candle view with live price overlay
- **Integration**: Both update automatically from TradingContext

#### Watchlist
- Currency pair list with real-time prices
- Click-to-select functionality
- Price flash animations and color coding
- EUR and USD section organization

### Development Patterns
- **Single-page architecture**: No authentication, direct terminal access
- **Real-time simulation**: Mock data with realistic market behavior
- **Component-based design**: Modular, reusable trading components
- **Context-driven updates**: Central state management with automatic UI updates
- **Professional UX**: Trading platform visual patterns and interactions

## 🧪 Testing

### Manual Testing
- Real-time price updates across all components
- Chart rendering and technical indicator functionality
- Symbol selection and chart synchronization
- Responsive design across different screen sizes

### Build Verification
```bash
# Verify build process
npm run build

# Check for syntax errors
npm run lint
```

## 📦 Dependencies

### Core Dependencies (33 packages)
- **React Ecosystem**: react, react-dom, react-router-dom
- **UI Framework**: @radix-ui components (essential set), lucide-react
- **Charts**: klinecharts, recharts
- **State Management**: @tanstack/react-query
- **Styling**: tailwindcss, tailwind-merge, class-variance-authority
- **Utilities**: clsx, sonner, cmdk, embla-carousel-react

### Development Dependencies
- **Build Tools**: vite, typescript, @vitejs/plugin-react-swc
- **Code Quality**: eslint, typescript-eslint
- **Types**: @types/react, @types/react-dom, @types/node

## 🚀 Deployment

### Production Build
```bash
# Create optimized production build
npm run build

# Build output
dist/
├── index.html
├── assets/
│   ├── index-[hash].css    # Styles (~70KB gzipped)
│   └── index-[hash].js     # Application (~158KB gzipped)
```

### Deployment Options
- **Static Hosting**: Vercel, Netlify, GitHub Pages
- **CDN**: Cloudflare, AWS CloudFront
- **Traditional Hosting**: Any web server with static file support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **KLineChart**: Professional trading charts and technical analysis
- **shadcn/ui**: Beautiful and accessible React UI components
- **TanStack Query**: Powerful data synchronization for React
- **Tailwind CSS**: Utility-first CSS framework
- **Vite**: Next generation frontend tooling

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

---

**Nexus Trader Terminal** - Professional trading simulation platform for individual traders.

### Recent Updates

**August 2025 - Major Codebase Cleanup**
- Removed 45+ legacy files (64% codebase reduction)
- Eliminated 52 unused dependencies
- Streamlined architecture for production readiness
- Maintained all core functionality while improving performance