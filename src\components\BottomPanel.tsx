import React, { useState } from 'react';
import { useTradingContext } from '../contexts/TradingContext';
import { PositionHistory } from './PositionHistory';
import { OrderHistory } from './OrderHistory';
import { DealHistory } from './DealHistory';

const OpenPositionsTab: React.FC = () => {
  const { openPositions } = useTradingContext();
  
  if (openPositions.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <div className="text-lg mb-2">📈</div>
          <div>No open positions</div>
          <div className="text-xs mt-1">Your open positions will appear here</div>
        </div>
      </div>
    );
  }

  const formatPrice = (price: number, symbol: string) => {
    const decimals = symbol.includes('JPY') ? 3 : 5;
    return price.toFixed(decimals);
  };

  const formatTPSL = (tp?: number, sl?: number, symbol?: string) => {
    const tpDisplay = tp ? formatPrice(tp, symbol || '') : '-';
    const slDisplay = sl ? formatPrice(sl, symbol || '') : '-';
    return `TP: ${tpDisplay} / SL: ${slDisplay}`;
  };

  return (
    <div className="h-full overflow-auto">
      <table className="w-full text-xs">
        <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0">
          <tr>
            <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Symbol</th>
            <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Type</th>
            <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Volume</th>
            <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Open Price</th>
            <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Current Price</th>
            <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Unrealized PNL</th>
            <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Realized PNL</th>
            <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">TP/SL</th>
          </tr>
        </thead>
        <tbody>
          {openPositions.map((position, index) => (
            <tr
              key={position.positionId}
              className={`border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50/50 dark:bg-gray-800/30'
              }`}
            >
              <td className="px-3 py-2 font-semibold text-gray-900 dark:text-white">
                {position.symbol}
              </td>
              <td className="px-3 py-2">
                <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                  position.type === 'BUY'
                    ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                    : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                }`}>
                  {position.type}
                </span>
              </td>
              <td className="px-3 py-2 text-right text-gray-900 dark:text-white">
                {position.volume}
              </td>
              <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                {formatPrice(position.openPrice, position.symbol)}
              </td>
              <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                {formatPrice(position.currentPrice, position.symbol)}
              </td>
              <td className="px-3 py-2 text-right">
                <span className={`font-semibold ${
                  position.pl >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {position.pl >= 0 ? '+' : ''}${position.pl.toFixed(2)}
                </span>
              </td>
              <td className="px-3 py-2 text-right text-gray-500 dark:text-gray-400">
                $0.00
              </td>
              <td className="px-3 py-2 text-left font-mono text-gray-700 dark:text-gray-300 text-xs">
                {formatTPSL(position.tp, position.sl, position.symbol)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const OpenOrdersTab: React.FC = () => {
  const { openOrders } = useTradingContext();
  
  if (openOrders.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <div className="text-lg mb-2">📊</div>
          <div>No open orders</div>
          <div className="text-xs mt-1">Your pending orders will appear here</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto">
      <table className="w-full text-xs">
        <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0">
          <tr>
            <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Order ID</th>
            <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Symbol</th>
            <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Type</th>
            <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Volume</th>
            <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Order Price</th>
            <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Current Price</th>
            <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">State</th>
            <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Comment</th>
          </tr>
        </thead>
        <tbody>
          {openOrders.map((order, index) => (
            <tr
              key={order.orderId}
              className={`border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50/50 dark:bg-gray-800/30'
              }`}
            >
              <td className="px-3 py-2 font-mono text-gray-600 dark:text-gray-400">
                {order.orderId}
              </td>
              <td className="px-3 py-2 font-semibold text-gray-900 dark:text-white">
                {order.symbol}
              </td>
              <td className="px-3 py-2">
                <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                  order.type.includes('BUY')
                    ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                    : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                }`}>
                  {order.type}
                </span>
              </td>
              <td className="px-3 py-2 text-right text-gray-900 dark:text-white">
                {order.volume}
              </td>
              <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                {order.orderPrice.toFixed(order.symbol.includes('JPY') ? 3 : 5)}
              </td>
              <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                {order.currentPrice}
              </td>
              <td className="px-3 py-2">
                <span className="px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                  {order.state}
                </span>
              </td>
              <td className="px-3 py-2 text-gray-600 dark:text-gray-400 max-w-32 truncate">
                {order.comment}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export const BottomPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('open-positions');
  const { openPositionsCount, openOrdersCount } = useTradingContext();

  const tabs = [
    { 
      id: 'open-positions', 
      label: `Open Positions (${openPositionsCount})`, 
      component: OpenPositionsTab 
    },
    { 
      id: 'open-orders', 
      label: `Open Orders (${openOrdersCount})`, 
      component: OpenOrdersTab 
    },
    { 
      id: 'position-history', 
      label: 'Position History', 
      component: PositionHistory 
    },
    { 
      id: 'order-history', 
      label: 'Order History', 
      component: OrderHistory 
    },
    { 
      id: 'deal-history', 
      label: 'Deal History', 
      component: DealHistory 
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || OpenPositionsTab;

  return (
    <div className="h-48 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Tab Navigation */}
      <div className="flex items-center border-b border-gray-200 dark:border-gray-700 px-4">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {tab.label}
          </button>
        ))}
        
        {/* Filter Controls */}
        <div className="ml-auto flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
          <span>Filtered by</span>
          <span className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">All</span>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        <ActiveComponent />
      </div>
    </div>
  );
};