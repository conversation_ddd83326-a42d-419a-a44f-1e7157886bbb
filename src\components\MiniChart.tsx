import React, { useEffect, useRef } from 'react';
import { init, dispose } from 'klinecharts';
import type { Chart } from 'klinecharts';
import { useKLineData } from '../hooks/useKLineData';
import { useTradingContext } from '../contexts/TradingContext';

export const MiniChart: React.FC = () => {
  const { selectedSymbol, getCurrentPair } = useTradingContext();
  const currentPair = getCurrentPair();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<Chart | null>(null);
  
  // Get mock candlestick data for mini chart (using 1h timeframe)
  const klineData = useKLineData(selectedSymbol, '1h');

  // Initialize mini chart
  useEffect(() => {
    if (!chartRef.current) return;

    // Create chart instance
    const chart = init(chartRef.current);
    chartInstanceRef.current = chart;

    // Configure minimal chart styles for mini view
    chart.setStyles({
      grid: {
        show: false
      },
      candle: {
        type: 'candle_solid',
        bar: {
          upColor: '#22C55E',
          downColor: '#EF4444',
          noChangeColor: '#9CA3AF',
        },
        tooltip: {
          showRule: 'none' // Hide tooltip for mini chart
        }
      },
      xAxis: {
        show: false
      },
      yAxis: {
        show: false
      },
      crosshair: {
        show: false
      }
    });

    // Cleanup function
    return () => {
      if (chartInstanceRef.current) {
        dispose(chartRef.current!);
        chartInstanceRef.current = null;
      }
    };
  }, []);

  // Update chart data when symbol changes
  useEffect(() => {
    if (chartInstanceRef.current && klineData.length > 0) {
      // For mini chart, show only recent data (last 20 candles)
      const recentData = klineData.slice(-20);
      chartInstanceRef.current.applyNewData(recentData, true);
    }
  }, [klineData]);

  // Handle chart resize
  useEffect(() => {
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="h-32 bg-white dark:bg-gray-900 border-r border-b border-gray-200 dark:border-gray-700 p-3">
      <div className="h-full flex flex-col">
        {/* Mini Chart Header */}
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Mini Chart</h3>
          <div className="text-xs text-gray-500 dark:text-gray-400">{selectedSymbol}</div>
        </div>
        
        {/* Mini Chart Area */}
        <div className="flex-1 relative">
          <div 
            ref={chartRef} 
            className="absolute inset-0 w-full h-full bg-gray-50 dark:bg-gray-800 rounded"
            style={{ minHeight: '60px' }}
          />
          
          {/* Price Change Overlay */}
          {currentPair && (
            <div className="absolute bottom-1 right-1">
              <div className={`text-xs font-bold px-1.5 py-0.5 rounded shadow-sm ${
                currentPair.changePercent >= 0 
                  ? 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/20' 
                  : 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
              }`}>
                {currentPair.changePercent >= 0 ? '↗' : '↘'} {Math.abs(currentPair.changePercent).toFixed(2)}%
              </div>
            </div>
          )}
          
          {/* Live Price Display */}
          {currentPair && (
            <div className="absolute top-1 left-1">
              <div className="text-xs font-medium text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-gray-800/80 px-1.5 py-0.5 rounded shadow-sm">
                {currentPair.ask.toFixed(currentPair.symbol.includes('JPY') ? 3 : 5)}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};