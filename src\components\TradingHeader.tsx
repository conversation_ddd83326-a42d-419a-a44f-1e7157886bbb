import React from 'react';
import { ChevronDown, RotateCcw, Save, Maximize } from 'lucide-react';

export const TradingHeader: React.FC = () => {
  const accountSummary = {
    balance: 50000.00,
    equity: 52150.75,
    margin: 1250.50,
    freeMargin: 50900.25,
    marginLevel: 4167.26
  };

  return (
    <header className="h-14 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
      {/* Left Section */}
      <div className="flex items-center space-x-6">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Nexus</h1>
        <div className="flex items-center space-x-1 text-sm">
          <span className="text-gray-600 dark:text-gray-400">Regular trading</span>
          <ChevronDown className="h-4 w-4 text-gray-600 dark:text-gray-400" />
        </div>
      </div>

      {/* Center Navigation */}
      <nav className="flex items-center space-x-1">
        {['Trade', 'Market', 'News', 'Ideas', 'Account'].map((item, index) => (
          <button
            key={item}
            className={`px-3 py-1.5 text-sm font-medium rounded transition-colors ${
              index === 0 
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' 
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {item}
          </button>
        ))}
      </nav>

      {/* Right Section - Account Summary */}
      <div className="flex items-center space-x-6 text-xs">
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400">Balance</div>
            <div className="font-medium text-gray-900 dark:text-white">${accountSummary.balance.toFixed(2)}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400">Equity</div>
            <div className="font-medium text-green-600">${accountSummary.equity.toFixed(2)}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400">Margin</div>
            <div className="font-medium text-gray-900 dark:text-white">${accountSummary.margin.toFixed(2)}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400">Free Margin</div>
            <div className="font-medium text-gray-900 dark:text-white">${accountSummary.freeMargin.toFixed(2)}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400">Margin Level</div>
            <div className="font-medium text-green-600">{accountSummary.marginLevel.toFixed(2)}%</div>
          </div>
        </div>
        
        {/* Action Icons */}
        <div className="flex items-center space-x-2">
          <button className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
            <RotateCcw className="h-4 w-4" />
          </button>
          <button className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
            <Save className="h-4 w-4" />
          </button>
          <button className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
            <Maximize className="h-4 w-4" />
          </button>
        </div>
      </div>
    </header>
  );
};