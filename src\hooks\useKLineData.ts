import { useMemo } from 'react';

export interface KLineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// Generate mock candlestick data for the selected symbol
export const useKLineData = (symbol: string, timeframe: string): KLineData[] => {
  return useMemo(() => {
    const data: KLineData[] = [];
    const now = Date.now();
    const timeframeMs = getTimeframeMs(timeframe);
    const basePrice = getBasePriceForSymbol(symbol);
    
    // Generate 100 candles of historical data
    for (let i = 99; i >= 0; i--) {
      const timestamp = now - (i * timeframeMs);
      const volatility = 0.01; // 1% volatility
      
      // Calculate OHLC with realistic price movements
      const previousClose = i === 99 ? basePrice : data[data.length - 1]?.close || basePrice;
      const change = (Math.random() - 0.5) * volatility * previousClose;
      const open = previousClose + change * 0.5;
      const close = open + change;
      
      // High and low based on open/close with some randomness
      const range = Math.abs(close - open) * (1 + Math.random());
      const high = Math.max(open, close) + range * Math.random();
      const low = Math.min(open, close) - range * Math.random();
      
      // Volume (random between 1000-10000)
      const volume = Math.floor(Math.random() * 9000) + 1000;
      
      data.push({
        timestamp,
        open: Number(open.toFixed(5)),
        high: Number(high.toFixed(5)),
        low: Number(low.toFixed(5)),
        close: Number(close.toFixed(5)),
        volume
      });
    }
    
    return data;
  }, [symbol, timeframe]);
};

// Get timeframe in milliseconds
const getTimeframeMs = (timeframe: string): number => {
  switch (timeframe) {
    case '1m': return 60 * 1000;
    case '5m': return 5 * 60 * 1000;
    case '15m': return 15 * 60 * 1000;
    case '30m': return 30 * 60 * 1000;
    case '1h': return 60 * 60 * 1000;
    case '4h': return 4 * 60 * 60 * 1000;
    case '1d': return 24 * 60 * 60 * 1000;
    default: return 60 * 60 * 1000; // Default to 1h
  }
};

// Get base price for different symbols
const getBasePriceForSymbol = (symbol: string): number => {
  switch (symbol) {
    case 'EURUSD': return 1.0542;
    case 'GBPUSD': return 1.2689;
    case 'USDJPY': return 149.87;
    case 'USDCHF': return 0.8875;
    case 'EURJPY': return 158.42;
    case 'EURGBP': return 0.8313;
    case 'EURCHF': return 0.9356;
    case 'AUDUSD': return 0.6532;
    default: return 1.0000;
  }
};