import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';

const mockPositions = [
  { symbol: 'EURUSD', side: 'buy', quantity: 0.1, profitLoss: 12.50 },
  { symbol: 'GBPUSD', side: 'sell', quantity: 0.05, profitLoss: -8.25 },
  { symbol: 'USDJPY', side: 'buy', quantity: 0.08, profitLoss: 15.75 },
];

export const PositionsPanel: React.FC = () => {
  return (
    <div className="flex-1 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 p-3 overflow-hidden">
      <div className="h-full flex flex-col">
        {/* Positions Header */}
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Positions</h3>
        </div>
        
        {/* Positions Table */}
        <div className="flex-1 overflow-y-auto">
          {mockPositions.length > 0 ? (
            <table className="w-full text-xs">
              <thead>
                <tr className="text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                  <th className="px-1 py-1 text-left font-medium">Side</th>
                  <th className="px-1 py-1 text-left font-medium">Symbol</th>
                  <th className="px-1 py-1 text-left font-medium">Qty</th>
                  <th className="px-1 py-1 text-left font-medium">P/L</th>
                </tr>
              </thead>
              <tbody>
                {mockPositions.map((position, index) => (
                  <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="px-1 py-1">
                      {position.side === 'buy' ? (
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      )}
                    </td>
                    <td className="px-1 py-1 text-gray-900 dark:text-white font-medium">
                      {position.symbol}
                    </td>
                    <td className="px-1 py-1 text-gray-700 dark:text-gray-300">
                      {position.quantity}
                    </td>
                    <td className={`px-1 py-1 font-medium ${
                      position.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {position.profitLoss >= 0 ? '+' : ''}${position.profitLoss.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <div className="text-lg mb-2">📈</div>
                <div>No positions</div>
                <div className="text-xs mt-1">Your open positions will appear here</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};